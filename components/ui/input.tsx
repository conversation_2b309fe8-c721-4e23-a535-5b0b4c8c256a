import * as React from 'react';
import { TextField } from '@radix-ui/themes';

import { cn } from '@/lib/utils';

export interface InputProps extends React.ComponentProps<typeof TextField.Root> {
  placeholder?: string;
}

const Input = React.forwardRef<
  React.ElementRef<typeof TextField.Root>,
  InputProps
>(({ className, placeholder, ...props }, ref) => {
  return (
    <TextField.Root
      className={cn(
        'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
        className
      )}
      placeholder={placeholder}
      ref={ref}
      {...props}
    />
  );
});
Input.displayName = 'Input';

export { Input };
