"use client";

import * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { type ThemeProviderProps } from "next-themes";
import { Theme } from "@radix-ui/themes";
import { useTheme } from "next-themes";

function RadixThemeWrapper({ children }: { children: React.ReactNode }) {
  const { theme, systemTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  // During SSR or before mounting, use dark theme as default
  if (!mounted) {
    return (
      <Theme
        accentColor="blue"
        grayColor="gray"
        radius="medium"
        scaling="100%"
        appearance="dark"
        panelBackground="solid"
        hasBackground={true}
      >
        {children}
      </Theme>
    );
  }

  // After mounting, use the actual theme
  const resolvedTheme = theme === "system" ? (systemTheme || "dark") : theme;

  return (
    <Theme
      accentColor="blue"
      grayColor="gray"
      radius="medium"
      scaling="100%"
      appearance={(resolvedTheme as "light" | "dark") || "dark"}
      panelBackground="solid"
      hasBackground={true}
    >
      {children}
    </Theme>
  );
}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider {...props}>
      <RadixThemeWrapper>
        {children}
      </RadixThemeWrapper>
    </NextThemesProvider>
  );
}