import { Metadata } from 'next';

export const metadata: Metadata = {
  title: "Dashboard Demo | OnlyRules - Text Readability Improvements",
  description: "Interactive demonstration of the improved text readability and visual hierarchy fixes in the OnlyRules dashboard. See the enhanced contrast and accessibility improvements.",
  keywords: "dashboard demo, text readability, accessibility, contrast improvements, visual hierarchy, OnlyRules",
  openGraph: {
    title: "Dashboard Demo - OnlyRules Text Readability Improvements",
    description: "See the enhanced contrast and accessibility improvements in our dashboard demo",
    type: "website",
  },
};

export default function DemoLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen">
      {children}
    </div>
  );
}
