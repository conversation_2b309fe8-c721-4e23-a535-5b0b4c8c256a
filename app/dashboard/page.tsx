"use client";

import { useEffect, useState } from "react";

// Force dynamic rendering for this page
export const dynamic = 'force-dynamic';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Plus, Search, Filter, Code, Users, Eye, Lock } from "lucide-react";
import { RuleCard } from "@/components/rule-card";
import { RuleEditor } from "@/components/rule-editor";
import { useSession } from "@/lib/auth-client";
import { Rule, Tag, RulePayload } from "@/lib/store";
import { toast } from "sonner";
import { Text, Heading, Box, Card as RadixCard, Flex } from "@radix-ui/themes";

export default function DashboardPage() {
  const { data: session } = useSession();
  const [rules, setRules] = useState<Rule[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedIDE, setSelectedIDE] = useState("ALL");
  const [showEditor, setShowEditor] = useState(false);
  const [editingRule, setEditingRule] = useState<Rule | null>(null);

  const fetchRules = async () => {
    try {
      const params = new URLSearchParams();
      if (searchQuery) params.set("search", searchQuery);
      if (selectedTags.length > 0) params.set("tags", selectedTags.join(","));
      if (selectedIDE !== "ALL") params.set("ideType", selectedIDE);

      const response = await fetch(`/api/rules?${params}`);
      const data = await response.json();
      setRules(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error("Error fetching rules:", error);
      toast.error("Failed to fetch rules");
      setRules([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchTags = async () => {
    try {
      const response = await fetch("/api/tags");
      const data = await response.json();
      setTags(data);
    } catch (error) {
      console.error("Error fetching tags:", error);
    }
  };

  useEffect(() => {
    fetchRules();
    fetchTags();
  }, [searchQuery, selectedTags, selectedIDE]);

  const handleCreateRule = () => {
    setEditingRule(null);
    setShowEditor(true);
  };

  const handleEditRule = (rule: Rule) => {
    setEditingRule(rule);
    setShowEditor(true);
  };

  const handleSaveRule = async (ruleData: Partial<RulePayload>) => {
    try {
      const method = editingRule ? "PUT" : "POST";
      const url = editingRule ? `/api/rules/${editingRule.id}` : "/api/rules";
      
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(ruleData),
      });

      if (response.ok) {
        toast.success(editingRule ? "Rule updated" : "Rule created");
        setShowEditor(false);
        fetchRules();
      } else {
        toast.error("Failed to save rule");
      }
    } catch (error) {
      console.error("Error saving rule:", error);
      toast.error("Failed to save rule");
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    try {
      const response = await fetch(`/api/rules/${ruleId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Rule deleted");
        fetchRules();
      } else {
        toast.error("Failed to delete rule");
      }
    } catch (error) {
      console.error("Error deleting rule:", error);
      toast.error("Failed to delete rule");
    }
  };

  const toggleTag = (tagName: string) => {
    setSelectedTags(prev =>
      prev.includes(tagName)
        ? prev.filter(t => t !== tagName)
        : [...prev, tagName]
    );
  };

  // Temporarily bypass authentication for testing text readability
  const isTestMode = process.env.NODE_ENV === 'development';

  if (!session?.user && !isTestMode) {
    return (
      <div className="container py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4" style={{ color: 'hsl(0 0% 98%)' }}>Please sign in to continue</h1>
          <Button asChild>
            <a href="/auth/signin">Sign In</a>
          </Button>
        </div>
      </div>
    );
  }

  // Use mock session in test mode
  const effectiveSession = session || (isTestMode ? {
    user: {
      id: "test-user-id",
      name: "Test User",
      email: "<EMAIL>",
      image: null
    }
  } : null);

  const userRules = Array.isArray(rules) ? rules.filter(rule => rule.userId === effectiveSession?.user?.id) : [];
  const publicRules = Array.isArray(rules) ? rules.filter(rule => rule.visibility === "PUBLIC") : [];

  const stats = {
    totalRules: userRules.length,
    publicRules: userRules.filter(r => r.visibility === "PUBLIC").length,
    privateRules: userRules.filter(r => r.visibility === "PRIVATE").length,
    totalViews: 0, // Would be calculated from analytics
  };

  return (
    <Box className="container py-8 space-y-8">
      {/* Header */}
      <Flex justify="between" align="center">
        <Box>
          <Heading size="8" weight="bold" color="gray" highContrast>Dashboard</Heading>
          <Text size="3" color="gray">
            Manage your AI prompt rules and explore the community
          </Text>
        </Box>
        <Button onClick={handleCreateRule}>
          <Plus className="mr-2 h-4 w-4" />
          New Rule
        </Button>
      </Flex>

      {/* Stats */}
      <Box className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <RadixCard>
          <Flex justify="between" align="center" pb="2">
            <Text size="2" weight="medium" color="gray" highContrast>Total Rules</Text>
            <Code className="h-4 w-4" style={{ color: 'var(--gray-11)' }} />
          </Flex>
          <Text size="7" weight="bold" color="gray" highContrast>{stats.totalRules}</Text>
        </RadixCard>

        <RadixCard>
          <Flex justify="between" align="center" pb="2">
            <Text size="2" weight="medium" color="gray" highContrast>Public Rules</Text>
            <Users className="h-4 w-4" style={{ color: 'var(--gray-11)' }} />
          </Flex>
          <Text size="7" weight="bold" color="gray" highContrast>{stats.publicRules}</Text>
        </RadixCard>

        <RadixCard>
          <Flex justify="between" align="center" pb="2">
            <Text size="2" weight="medium" color="gray" highContrast>Private Rules</Text>
            <Lock className="h-4 w-4" style={{ color: 'var(--gray-11)' }} />
          </Flex>
          <Text size="7" weight="bold" color="gray" highContrast>{stats.privateRules}</Text>
        </RadixCard>

        <RadixCard>
          <Flex justify="between" align="center" pb="2">
            <Text size="2" weight="medium" color="gray" highContrast>Total Views</Text>
            <Eye className="h-4 w-4" style={{ color: 'var(--gray-11)' }} />
          </Flex>
          <Text size="7" weight="bold" color="gray" highContrast>{stats.totalViews}</Text>
        </RadixCard>
      </Box>

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search rules..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={selectedIDE} onValueChange={setSelectedIDE}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="IDE Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">All IDEs</SelectItem>
            <SelectItem value="GENERAL">General</SelectItem>
            <SelectItem value="CURSOR">Cursor</SelectItem>
            <SelectItem value="AUGMENT">Augment Code</SelectItem>
            <SelectItem value="WINDSURF">Windsurf</SelectItem>
            <SelectItem value="CLAUDE">Claude</SelectItem>
            <SelectItem value="GITHUB_COPILOT">GitHub Copilot</SelectItem>
            <SelectItem value="GEMINI">Gemini</SelectItem>
            <SelectItem value="OPENAI_CODEX">OpenAI Codex</SelectItem>
            <SelectItem value="CLINE">Cline</SelectItem>
            <SelectItem value="JUNIE">Junie</SelectItem>
            <SelectItem value="TRAE">Trae</SelectItem>
            <SelectItem value="LINGMA">Lingma</SelectItem>
            <SelectItem value="KIRO">Kiro</SelectItem>
            <SelectItem value="TENCENT_CODEBUDDY">Tencent Cloud CodeBuddy</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Tag Filters */}
      {tags.length > 0 && (
        <Box className="space-y-2">
          <Flex align="center" gap="2">
            <Filter className="h-4 w-4" style={{ color: 'var(--gray-12)' }} />
            <Text size="2" weight="medium" color="gray" highContrast>Filter by tags:</Text>
          </Flex>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Badge
                key={tag.id}
                variant={selectedTags.includes(tag.name) ? "solid" : "outline"}
                className="cursor-pointer"
                onClick={() => toggleTag(tag.name)}
                style={{
                  borderColor: tag.color,
                  backgroundColor: selectedTags.includes(tag.name) ? tag.color : "transparent",
                }}
              >
                {tag.name}
              </Badge>
            ))}
          </div>
        </Box>
      )}

      {/* Content */}
      <Tabs defaultValue="my-rules" className="space-y-6">
        <TabsList>
          <TabsTrigger value="my-rules">My Rules ({userRules.length})</TabsTrigger>
          <TabsTrigger value="community">Community ({publicRules.length})</TabsTrigger>
        </TabsList>
        
        <TabsContent value="my-rules" className="space-y-6">
          {userRules.length === 0 ? (
            <Card>
              <CardContent className="py-12 text-center">
                <Code className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No rules yet</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first AI prompt rule to get started
                </p>
                <Button onClick={handleCreateRule}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Rule
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {userRules.map((rule) => (
                <RuleCard
                  key={rule.id}
                  rule={rule}
                  onEdit={handleEditRule}
                  onDelete={handleDeleteRule}
                  isOwner={true}
                />
              ))}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="community" className="space-y-6">
          {publicRules.length === 0 ? (
            <Card>
              <CardContent className="py-12 text-center">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No community rules found</h3>
                <p className="text-muted-foreground">
                  Try adjusting your filters or check back later
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {publicRules.map((rule) => (
                <RuleCard
                  key={rule.id}
                  rule={rule}
                  isOwner={rule.userId === effectiveSession?.user?.id}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Rule Editor Dialog */}
      <Dialog open={showEditor} onOpenChange={setShowEditor}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>
              {editingRule ? "Edit Rule" : "Create New Rule"}
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto max-h-[70vh]">
            <RuleEditor
              rule={editingRule || undefined}
              onSave={handleSaveRule}
              onCancel={() => setShowEditor(false)}
            />
          </div>
        </DialogContent>
      </Dialog>
    </Box>
  );
}